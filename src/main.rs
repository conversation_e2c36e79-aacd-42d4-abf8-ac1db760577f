use anyhow::{Context, Result};
use rayon::prelude::*;
use serde::Deserialize;
use std::collections::HashMap;
use std::io::{self, Write};
use std::path::{Path, PathBuf};
use std::process::Command;
use std::sync::{Arc, Mutex};

// --- 数据结构定义 ---

// 用于从 ffprobe JSON 输出中反序列化原始流信息
#[derive(Debug, Deserialize)]
struct FfprobeStream {
    codec_type: String,
    codec_name: String,
    width: Option<u32>,
    height: Option<u32>,
    #[serde(default)]
    bit_rate: Option<String>,
    sample_rate: Option<String>,
    channels: Option<u32>,
    r_frame_rate: Option<String>, // e.g., "24/1" or "30000/1001"
}

// ffprobe JSON 输出的顶层结构
#[derive(Debug, Deserialize)]
struct FfprobeOutput {
    streams: Vec<FfprobeStream>,
}

// 清理和整合后的轨道信息，用于CSV和最终报告
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
struct TrackInfo {
    file_path: String,
    track_type: String, // "video" or "audio"
    codec: String,
    resolution: Option<String>,
    bitrate_kbps: Option<u64>,
    frame_rate: Option<f64>,
    sample_rate_khz: Option<f64>,
    channels: Option<u32>,
}

// --- 主逻辑 ---

fn main() -> Result<()> {
    println!("--- 视频文件分析工具 ---");

    // 1. 获取用户输入的路径
    let mut root_path = String::new();
    print!("请输入要扫描的文件夹绝对路径: ");
    io::stdout().flush()?;
    io::stdin().read_line(&mut root_path)?;
    let root_path = root_path.trim();

    if !Path::new(root_path).is_dir() {
        println!("错误: '{}' 不是一个有效的目录。", root_path);
        return Ok(());
    }

    println!("正在扫描 '{}'...", root_path);

    // 2. 查找所有视频文件
    let video_extensions: Vec<&str> = vec!["mp4", "mkv", "avi", "mov", "webm", "flv", "wmv"];
    let files_to_process: Vec<PathBuf> = walkdir::WalkDir::new(root_path)
        .into_iter()
        .filter_map(Result::ok)
        .filter(|e| e.file_type().is_file())
        .filter(|e| {
            e.path()
                .extension()
                .and_then(|s| s.to_str())
                .map(|s| video_extensions.contains(&s.to_lowercase().as_str()))
                .unwrap_or(false)
        })
        .map(|e| e.into_path())
        .collect();

    let total_files = files_to_process.len();
    if total_files == 0 {
        println!("未找到任何支持的视频文件。");
        return Ok(());
    }
    println!("找到 {} 个视频文件，开始分析...", total_files);

    // 使用Arc<Mutex>来安全地在线程间共享进度计数器
    let processed_count = Arc::new(Mutex::new(0));

    // 3. 使用 Rayon 并发处理文件
    let all_tracks: Vec<TrackInfo> = files_to_process
        .par_iter()
        .flat_map(|path| {
            let result = analyze_media_file(path);

            // 更新并打印进度
            let mut count = processed_count.lock().unwrap();
            *count += 1;
            print!("\r处理进度: {}/{}...", *count, total_files);
            io::stdout().flush().unwrap();

            match result {
                Ok(tracks) => tracks,
                Err(e) => {
                    // 在并发处理中，打印错误而不是让整个程序崩溃
                    eprintln!("\n警告: 处理文件 '{}' 失败: {}", path.display(), e);
                    vec![]
                }
            }
        })
        .collect();

    println!("\n\n分析完成！");

    if all_tracks.is_empty() {
        println!("未能从任何文件中提取出有效的视频或音频轨道信息。");
        return Ok(());
    }

    // 4. 写入 CSV 文件到目标文件夹
    let csv_path = Path::new(root_path).join("video_analysis_report.csv");
    write_to_csv(&all_tracks, csv_path.to_str().context("CSV路径包含无效字符")?)?;
    println!("详细信息已写入 '{}'", csv_path.display());

    // 5. 打印控制台汇总报告
    print_summary_report(&all_tracks);

    Ok(())
}

/// 调用 ffprobe 分析单个媒体文件并返回轨道信息
fn analyze_media_file(path: &Path) -> Result<Vec<TrackInfo>> {
    let output = Command::new("ffprobe")
        .args([
            "-v",
            "quiet",
            "-print_format",
            "json",
            "-show_streams",
            path.to_str().context("路径包含无效的UTF-8字符")?,
        ])
        .output()
        .context("执行 ffprobe 命令失败。请确保 ffmpeg 已安装并在系统 PATH 中。")?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(anyhow::anyhow!("ffprobe 执行出错: {}", stderr));
    }

    let ffprobe_data: FfprobeOutput =
        serde_json::from_slice(&output.stdout).context("解析 ffprobe 的 JSON 输出失败")?;

    let tracks: Vec<TrackInfo> = ffprobe_data
        .streams
        .into_iter()
        .map(|s| {
            let bitrate_kbps = s
                .bit_rate
                .and_then(|b| b.parse::<u64>().ok())
                .map(|b| b / 1000);

            let resolution = match (s.width, s.height) {
                (Some(w), Some(h)) if w > 0 && h > 0 => Some(format!("{}x{}", w, h)),
                _ => None,
            };

            let frame_rate = s.r_frame_rate.and_then(|fr_str| {
                let parts: Vec<&str> = fr_str.split('/').collect();
                if parts.len() == 2 {
                    let num = parts[0].parse::<f64>().ok()?;
                    let den = parts[1].parse::<f64>().ok()?;
                    if den != 0.0 {
                        Some(num / den)
                    } else {
                        None
                    }
                } else {
                    None
                }
            });

            let sample_rate_khz = s
                .sample_rate
                .and_then(|sr| sr.parse::<f64>().ok())
                .map(|sr| sr / 1000.0);

            TrackInfo {
                file_path: path.to_string_lossy().to_string(),
                track_type: s.codec_type,
                codec: s.codec_name,
                resolution,
                bitrate_kbps,
                frame_rate,
                sample_rate_khz,
                channels: s.channels,
            }
        })
        .collect();

    Ok(tracks)
}

/// 将提取的轨道信息写入CSV文件
fn write_to_csv(tracks: &[TrackInfo], file_name: &str) -> Result<()> {
    let mut wtr =
        csv::Writer::from_path(file_name).context(format!("无法创建 CSV 文件 '{}'", file_name))?;

    wtr.write_record([
        "FilePath",
        "TrackType",
        "Codec",
        "Resolution",
        "Bitrate(kbps)",
        "FrameRate(fps)",
        "SampleRate(kHz)",
        "Channels",
    ])?;

    for track in tracks {
        wtr.write_record(&[
            track.file_path.clone(),
            track.track_type.clone(),
            track.codec.clone(),
            track.resolution.clone().unwrap_or_default(),
            track
                .bitrate_kbps
                .map(|b| b.to_string())
                .unwrap_or_default(),
            track
                .frame_rate
                .map(|f| format!("{:.2}", f))
                .unwrap_or_default(),
            track
                .sample_rate_khz
                .map(|s| s.to_string())
                .unwrap_or_default(),
            track.channels.map(|c| c.to_string()).unwrap_or_default(),
        ])?;
    }

    wtr.flush()?;
    Ok(())
}

/// 在控制台打印格式化的汇总报告
fn print_summary_report(tracks: &[TrackInfo]) {
    println!("\n--- 媒体库质量汇总报告 ---");

    // 分离视频和音频轨道
    let (video_tracks, audio_tracks): (Vec<_>, Vec<_>) = tracks
        .iter()
        .cloned()
        .partition(|t| t.track_type == "video");

    // --- 视频报告 ---
    if !video_tracks.is_empty() {
        println!("\n[ 视频信息 ({} 个轨道) ]", video_tracks.len());

        // 1. 分辨率汇总与排序
        let mut resolutions = count_occurrences(&video_tracks, |t| {
            t.resolution.clone().unwrap_or_else(|| "未知".to_string())
        });
        // 排序逻辑：按总像素数降序排
        resolutions.sort_by(|(res_a, _), (res_b, _)| {
            let pixels_a = parse_resolution(res_a).unwrap_or(0);
            let pixels_b = parse_resolution(res_b).unwrap_or(0);
            pixels_b.cmp(&pixels_a)
        });
        print_category("分辨率 (按质量排序)", &resolutions);

        // 2. 视频编码格式汇总与排序
        let mut codecs = count_occurrences(&video_tracks, |t| t.codec.clone());
        codecs.sort_by(|(_, count_a), (_, count_b)| count_b.cmp(count_a));
        print_category("视频编码格式 (按数量排序)", &codecs);

        // 3. 视频码率汇总与排序
        let bitrate_ranges = summarize_bitrate(&video_tracks);
        print_category("视频码率分布 (kbps)", &bitrate_ranges);
    }

    // --- 音频报告 ---
    if !audio_tracks.is_empty() {
        println!("\n[ 音频信息 ({} 个轨道) ]", audio_tracks.len());

        // 1. 音频编码格式汇总与排序
        let mut codecs = count_occurrences(&audio_tracks, |t| t.codec.clone());
        codecs.sort_by(|(_, count_a), (_, count_b)| count_b.cmp(count_a));
        print_category("音频编码格式 (按数量排序)", &codecs);

        // 2. 音频码率汇总与排序
        let bitrate_ranges = summarize_bitrate(&audio_tracks);
        print_category("音频码率分布 (kbps)", &bitrate_ranges);

        // 3. 声道汇总与排序
        let mut channels = count_occurrences(&audio_tracks, |t| {
            t.channels
                .map(|c| format!("{} 声道", c))
                .unwrap_or_else(|| "未知".to_string())
        });
        channels.sort_by(|(ch_a, _), (ch_b, _)| {
            let num_a = ch_a.chars().next().unwrap_or('0').to_digit(10).unwrap_or(0);
            let num_b = ch_b.chars().next().unwrap_or('0').to_digit(10).unwrap_or(0);
            num_b.cmp(&num_a) // 降序
        });
        print_category("声道 (按质量排序)", &channels);

        // 4. 采样率汇总与排序
        let mut sample_rates = count_occurrences(&audio_tracks, |t| {
            t.sample_rate_khz
                .map(|s| format!("{:.1} kHz", s))
                .unwrap_or_else(|| "未知".to_string())
        });
        sample_rates.sort_by(|(sr_a, _), (sr_b, _)| {
            let val_a: f64 = sr_a
                .split_whitespace()
                .next()
                .unwrap_or("0.0")
                .parse()
                .unwrap_or(0.0);
            let val_b: f64 = sr_b
                .split_whitespace()
                .next()
                .unwrap_or("0.0")
                .parse()
                .unwrap_or(0.0);
            val_b
                .partial_cmp(&val_a)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        print_category("采样率 (按质量排序)", &sample_rates);
    }

    println!("\n--- 报告结束 ---");
}

// --- 辅助函数 ---

/// 泛型函数，用于统计一个属性出现的次数
fn count_occurrences<F>(tracks: &[TrackInfo], extractor: F) -> Vec<(String, usize)>
where
    F: Fn(&TrackInfo) -> String,
{
    let mut counts = HashMap::new();
    for track in tracks {
        *counts.entry(extractor(track)).or_insert(0) += 1;
    }
    counts.into_iter().collect()
}

/// 解析分辨率字符串 "WxH" 并返回总像素数
fn parse_resolution(res: &str) -> Option<u64> {
    let parts: Vec<&str> = res.split('x').collect();
    if parts.len() == 2 {
        let w = parts[0].parse::<u64>().ok()?;
        let h = parts[1].parse::<u64>().ok()?;
        Some(w * h)
    } else {
        None
    }
}

/// 汇总码率到不同的范围
fn summarize_bitrate(tracks: &[TrackInfo]) -> Vec<(String, usize)> {
    let mut ranges = HashMap::new();
    // 定义码率范围
    let boundaries = [
        (0, 500),
        (500, 1000),
        (1000, 2000),
        (2000, 5000),
        (5000, 10000),
        (10000, 20000),
        (20000, 50000),
    ];

    for track in tracks {
        if let Some(bitrate) = track.bitrate_kbps {
            let mut categorized = false;
            for &(lower, upper) in &boundaries {
                if bitrate >= lower && bitrate < upper {
                    *ranges
                        .entry(format!("{}-{} kbps", lower, upper))
                        .or_insert(0) += 1;
                    categorized = true;
                    break;
                }
            }
            if !categorized {
                *ranges.entry("> 50000 kbps".to_string()).or_insert(0) += 1;
            }
        } else {
            *ranges.entry("未知码率".to_string()).or_insert(0) += 1;
        }
    }

    let mut sorted_ranges: Vec<(String, usize)> = ranges.into_iter().collect();
    // 按码率范围的下限排序（降序）
    sorted_ranges.sort_by(|(r_a, _), (r_b, _)| {
        let num_a = r_a
            .split('-')
            .next()
            .unwrap_or("0")
            .parse::<u32>()
            .unwrap_or(0);
        let num_b = r_b
            .split('-')
            .next()
            .unwrap_or("0")
            .parse::<u32>()
            .unwrap_or(0);
        num_b.cmp(&num_a)
    });

    sorted_ranges
}

/// 格式化打印一个分类的汇总信息
fn print_category(title: &str, data: &[(String, usize)]) {
    println!("\n  - {}:", title);
    if data.is_empty() {
        println!("    (无数据)");
        return;
    }
    let max_key_len = data.iter().map(|(k, _)| k.len()).max().unwrap_or(0);
    for (key, count) in data {
        println!("    {:width$} : {} 个文件", key, count, width = max_key_len);
    }
}
