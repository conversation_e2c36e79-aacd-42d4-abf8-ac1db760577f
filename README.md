# Video Analyzer / 视频文件分析工具

一个高性能的视频文件分析工具，用于批量扫描和分析视频文件的技术参数，生成详细的质量报告。

## 功能特性

- 🚀 **高性能并发处理** - 使用 Rayon 并行处理多个文件，大幅提升分析速度
- 📊 **详细技术分析** - 提取视频和音频轨道的完整技术参数
- 📈 **智能汇总报告** - 按质量和数量自动排序，生成易读的汇总统计
- 📄 **CSV 导出** - 生成详细的 CSV 报告，便于进一步数据分析
- 🔍 **多格式支持** - 支持主流视频格式：MP4、MKV、AVI、MOV、WebM、FLV、WMV
- 🛡️ **错误容错** - 单个文件处理失败不影响整体分析进程

## 系统要求

### 必需依赖
- **Rust** (2024 edition 或更高版本)
- **FFmpeg** - 必须安装并添加到系统 PATH 中
  - Windows: 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载
  - macOS: `brew install ffmpeg`
  - Ubuntu/Debian: `sudo apt install ffmpeg`
  - CentOS/RHEL: `sudo yum install ffmpeg`

### 验证 FFmpeg 安装
```bash
ffprobe -version
```

## 安装与使用

### 1. 克隆项目
```bash
git clone <repository-url>
cd video-analyzer-rs
```

### 2. 编译项目
```bash
cargo build --release
```

### 3. 运行程序
```bash
cargo run --release
```

或者直接运行编译后的可执行文件：
```bash
./target/release/video-analyzer-rs
```

### 4. 使用说明
1. 程序启动后，输入要扫描的文件夹绝对路径
2. 程序会自动扫描该目录及所有子目录中的视频文件
3. 实时显示处理进度
4. 分析完成后会生成两种输出：
   - **控制台汇总报告** - 按质量排序的统计信息
   - **CSV详细报告** - 保存为 `video_analysis_report.csv`

## 输出说明

### 控制台汇总报告
程序会在控制台输出格式化的汇总报告，包括：

#### 视频信息
- **分辨率分布** (按像素总数降序排列)
- **编码格式统计** (按使用频率降序排列)  
- **码率分布** (按码率范围分组)

#### 音频信息
- **编码格式统计** (按使用频率降序排列)
- **码率分布** (按码率范围分组)
- **声道配置** (按声道数降序排列)
- **采样率分布** (按采样率降序排列)

### CSV 详细报告
生成的 `video_analysis_report.csv` 包含每个轨道的详细信息：

| 字段 | 说明 |
|------|------|
| FilePath | 文件完整路径 |
| TrackType | 轨道类型 (video/audio) |
| Codec | 编码格式 |
| Resolution | 分辨率 (仅视频) |
| Bitrate(kbps) | 码率 (千比特每秒) |
| FrameRate(fps) | 帧率 (仅视频) |
| SampleRate(kHz) | 采样率 (仅音频) |
| Channels | 声道数 (仅音频) |

## 项目结构

```
video-analyzer-rs/
├── Cargo.toml          # 项目配置和依赖
├── Cargo.lock          # 依赖版本锁定
├── src/
│   └── main.rs         # 主程序源码
├── .gitignore          # Git 忽略文件配置
└── README.md           # 项目说明文档
```

## 技术架构

### 核心依赖
- **walkdir** (2.x) - 递归目录遍历
- **rayon** (1.10) - 数据并行处理框架
- **serde** (1.0) - JSON 序列化/反序列化
- **serde_json** (1.0) - JSON 处理
- **csv** (1.3) - CSV 文件生成
- **anyhow** (1.0) - 错误处理简化

### 数据流程
1. **文件发现** - 使用 `walkdir` 递归扫描目录
2. **并发分析** - 使用 `rayon` 并行调用 `ffprobe`
3. **数据解析** - 使用 `serde` 解析 ffprobe 的 JSON 输出
4. **结果汇总** - 统计分析并按质量/频率排序
5. **报告生成** - 输出控制台报告和 CSV 文件

### 性能优化
- **并行处理**: 利用多核 CPU 同时分析多个文件
- **内存效率**: 流式处理，避免加载大量数据到内存
- **错误隔离**: 单个文件失败不影响整体处理
- **进度反馈**: 实时显示处理进度

## 使用示例

### 示例输出
```
--- 视频文件分析工具 ---
请输入要扫描的文件夹绝对路径: /Users/<USER>/Movies
正在扫描 '/Users/<USER>/Movies'...
找到 150 个视频文件，开始分析...
处理进度: 150/150...

分析完成！
详细信息已写入 'video_analysis_report.csv'

--- 媒体库质量汇总报告 ---

[ 视频信息 (150 个轨道) ]

  - 分辨率 (按质量排序):
    3840x2160 : 45 个文件
    1920x1080 : 78 个文件
    1280x720  : 25 个文件
    未知      : 2 个文件

  - 视频编码格式 (按数量排序):
    h264 : 95 个文件
    hevc : 35 个文件
    vp9  : 15 个文件
    av1  : 5 个文件

[ 音频信息 (180 个轨道) ]

  - 音频编码格式 (按数量排序):
    aac : 120 个文件
    ac3 : 35 个文件
    dts : 25 个文件

--- 报告结束 ---
```

## 故障排除

### 常见问题

1. **"ffprobe 命令失败"**
   - 确保 FFmpeg 已正确安装
   - 检查 ffprobe 是否在系统 PATH 中
   - 尝试在命令行直接运行 `ffprobe -version`

2. **"路径包含无效的UTF-8字符"**
   - 确保文件路径不包含特殊字符
   - 在 Windows 上避免使用非 ASCII 字符的路径

3. **处理速度慢**
   - 程序会自动使用所有可用 CPU 核心
   - 处理速度主要受限于存储设备 I/O 性能
   - SSD 比机械硬盘处理速度更快

4. **内存使用过高**
   - 程序设计为流式处理，正常情况下内存使用较低
   - 如遇问题，可能是某些损坏的视频文件导致

## 开发说明

### 编译选项
```bash
# 开发模式 (包含调试信息)
cargo build

# 发布模式 (性能优化)
cargo build --release

# 运行测试
cargo test

# 代码格式化
cargo fmt

# 代码检查
cargo clippy
```

### 扩展开发
项目采用模块化设计，易于扩展：
- 添加新的视频格式支持：修改 `video_extensions` 数组
- 自定义输出格式：修改 `print_summary_report` 函数
- 添加新的分析维度：扩展 `TrackInfo` 结构体

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 更新日志

### v0.1.0
- 初始版本发布
- 支持主流视频格式分析
- 并发处理和进度显示
- CSV 报告导出
- 智能汇总统计